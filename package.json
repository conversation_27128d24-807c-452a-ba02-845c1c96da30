{"name": "ruoyi-plus-vben", "version": "1.4.4", "homepage": "https://gitee.com/dapppp/ruoyi-plus-vben.git", "bugs": {"url": "https://gitee.com/dapppp/ruoyi-plus-vben/issues"}, "repository": {"type": "git", "url": "git+https://gitee.com/dapppp/ruoyi-plus-vben.git"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"bootstrap": "pnpm install", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 pnpm vite build", "build:no-cache": "pnpm store prune && npm run build", "build:test": "cross-env NODE_OPTIONS=--max-old-space-size=8192 pnpm vite build --mode test", "commit": "czg", "dev": "pnpm vite", "preinstall": "npx only-allow pnpm", "postinstall": "turbo run stub", "lint": "turbo run lint", "lint:eslint": "eslint --cache --max-warnings 0  \"./src/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write .", "lint:stylelint": "stylelint \"**/*.{vue,css,less,scss}\" --fix --cache --cache-location node_modules/.cache/stylelint/", "log": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepare": "husky install", "preview": "vite preview", "reinstall": "rimraf pnpm-lock.yaml && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "serve": "npm run dev", "test:gzip": "npx http-server dist --cors --gzip -c-1", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["prettier --write", "eslint --fix", "stylelint --fix"], "*.{scss,less,styl,html}": ["prettier --write", "stylelint --fix"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "dependencies": {"@ant-design/icons-vue": "7.0.1", "@iconify/iconify": "3.1.1", "@logicflow/core": "1.2.26", "@logicflow/extension": "1.2.26", "@tinymce/tinymce-vue": "6.0.1", "@vben/hooks": "workspace:*", "@vue/shared": "3.4.37", "@vueuse/core": "^10.11.1", "@zxcvbn-ts/core": "3.0.4", "ant-design-vue": "4.2.3", "axios": "1.7.4", "bpmn-js": "17.5.0", "bpmn-js-token-simulation": "0.34.1", "codemirror": "5.65.16", "cropperjs": "1.6.2", "crypto-js": "4.2.0", "dayjs": "1.11.12", "diagram-js": "14.6.0", "diagram-js-minimap": "4.1.0", "didi": "10.2.2", "driver.js": "1.3.1", "echarts": "5.5.1", "exceljs": "4.4.0", "html2canvas": "1.4.1", "jsencrypt": "3.3.2", "lodash-es": "4.17.21", "nprogress": "0.2.0", "path-to-regexp": "6.2.2", "pinia": "2.1.7", "pinia-plugin-persistedstate": "3.2.1", "print-js": "1.6.0", "qrcode": "1.5.3", "qs": "6.12.1", "resize-observer-polyfill": "1.5.1", "showdown": "2.1.0", "sortablejs": "1.15.2", "tiny-svg": "4.1.1", "tinymce": "7.3.0", "unocss": "0.62.1", "vditor": "3.10.5", "vue": "3.4.37", "vue-i18n": "9.13.1", "vue-json-pretty": "2.4.0", "vue-router": "4.4.3", "vue-types": "5.1.3", "vue3-colorpicker": "2.3.0", "vuedraggable": "4.1.0", "vxe-table": "4.6.17", "vxe-table-plugin-export-xlsx": "4.0.1", "xe-utils": "3.5.25", "xlsx": "0.18.5"}, "devDependencies": {"@commitlint/cli": "19.3.0", "@commitlint/config-conventional": "19.2.2", "@iconify/json": "2.2.203", "@purge-icons/generated": "0.10.0", "@types/codemirror": "5.60.15", "@types/crypto-js": "4.2.2", "@types/lodash-es": "4.17.12", "@types/nprogress": "0.2.3", "@types/qrcode": "1.5.5", "@types/qs": "6.9.15", "@types/showdown": "2.0.6", "@types/sortablejs": "1.15.8", "@vben/eslint-config": "workspace:*", "@vben/stylelint-config": "workspace:*", "@vben/ts-config": "workspace:*", "@vben/types": "workspace:*", "@vben/vite-config": "workspace:*", "@vue/compiler-sfc": "3.4.37", "@vue/test-utils": "2.4.5", "@unocss/transformer-directives": "0.62.1", "@viarotel-org/unocss-preset-shades": "0.8.2", "conventional-changelog-cli": "4.1.0", "cross-env": "7.0.3", "cz-git": "1.9.1", "czg": "1.9.1", "lint-staged": "15.2.2", "prettier": "3.3.3", "prettier-plugin-packagejson": "2.5.0", "rimraf": "5.0.5", "sass": "1.77.6", "turbo": "1.13.2", "typescript": "5.5.4", "unbuild": "2.0.0", "vite": "5.4.0", "vite-plugin-vue-devtools": "7.3.8", "vue-tsc": "2.0.29"}, "engines": {"node": ">=18.12.0", "pnpm": ">=9.0.4"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}